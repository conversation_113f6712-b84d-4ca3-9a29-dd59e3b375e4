# Render.com Deployment Guide for CI/CD

This guide explains how to set up your application for deployment on Render.com using the GitHub Actions CI/CD pipeline.

## 🚀 Quick Setup Checklist

- [ ] Create Render.com account and service
- [ ] Configure GitHub repository secrets
- [ ] Set up environment variables on Render
- [ ] Test the deployment pipeline

## 1. Setting up Render.com

### Create a New Web Service

1. **Sign up/Login** to [Render.com](https://render.com)
2. **Create a new Web Service**:
   - Connect your GitHub repository
   - Choose "Docker" as the environment
   - Set the following configuration:

### Render Service Configuration

```yaml
# Basic Settings
Name: adopte1etudiant
Environment: Docker
Region: Choose closest to your users (e.g., Frankfurt for Europe)

# Build & Deploy
Dockerfile Path: ./Dockerfile
Docker Context Directory: . (root)

# Service Details
Port: 3001
Health Check Path: /api/users
```

### Environment Variables on Render

Set these environment variables in your Render service dashboard:

```bash
# Application
NODE_ENV=production
PORT=3001

# Database
MONGODB_URI=mongodb+srv://username:<EMAIL>/adopte1etudiant?retryWrites=true&w=majority

# Authentication & Security
JWT_SECRET=your-super-secure-jwt-secret-min-32-chars
SESSION_SECRET=your-super-secure-session-secret-min-32-chars
ADMIN_COOKIE_PASSWORD=your-admin-cookie-password
ADMIN_COOKIE_NAME=admin-bro-cookie

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=your-secure-admin-password

# Frontend URL (will be your Render URL)
FRONT_URL=https://your-app-name.onrender.com
```

### Generate Secure Secrets

Use these commands to generate secure secrets:

```bash
# JWT Secret (32+ characters)
openssl rand -base64 32

# Session Secret (32+ characters)
openssl rand -base64 32

# Admin Cookie Password (32+ characters)
openssl rand -base64 32
```

## 2. GitHub Repository Secrets

Add these secrets to your GitHub repository:

### Required Secrets

Go to **Settings > Secrets and variables > Actions** in your GitHub repository:

| Secret Name | Description | How to Get |
|-------------|-------------|------------|
| `RENDER_API_KEY` | Your Render API key | Account Settings > API Keys |
| `RENDER_SERVICE_ID` | Your service ID | Service Dashboard > Settings |

### Getting Render API Key

1. Go to [Render Dashboard](https://dashboard.render.com)
2. Click on your profile (top right)
3. Go to **Account Settings**
4. Navigate to **API Keys** tab
5. Click **Create API Key**
6. Copy the generated key

### Getting Render Service ID

1. Go to your service dashboard on Render
2. The Service ID is in the URL: `https://dashboard.render.com/web/srv-XXXXXXXXX`
3. Copy the `srv-XXXXXXXXX` part

## 3. MongoDB Setup

### Using MongoDB Atlas (Recommended)

1. **Create a MongoDB Atlas account** at [mongodb.com](https://www.mongodb.com/cloud/atlas)
2. **Create a new cluster** (free tier available)
3. **Create a database user**:
   - Username: `adopte1etudiant`
   - Password: Generate a secure password
4. **Whitelist IP addresses**:
   - Add `0.0.0.0/0` for Render (or specific Render IPs)
5. **Get connection string**:
   ```
   mongodb+srv://adopte1etudiant:<EMAIL>/adopte1etudiant?retryWrites=true&w=majority
   ```

## 4. Testing the Pipeline

### Trigger Deployment

1. **Create a Pull Request** to the `main` branch
   - This will run tests but NOT deploy
   - Check that all tests pass

2. **Merge to main branch**
   - This will run tests AND deploy to Render
   - Monitor the GitHub Actions tab for progress

### Monitoring Deployment

- **GitHub Actions**: Check the workflow progress
- **Render Dashboard**: Monitor build and deployment logs
- **Application Health**: Visit your deployed URL

## 5. Workflow Behavior

### On Pull Request
```
✅ Backend Tests
✅ Frontend Tests  
✅ E2E Tests (Cypress smoke tests)
✅ Build Application
❌ Deploy (skipped for PRs)
```

### On Push to Main
```
✅ Backend Tests
✅ Frontend Tests
✅ Build Application
✅ Deploy to Render
```

## 6. Troubleshooting

### Common Issues

#### Build Failures
```bash
# Check Node.js version compatibility
# Ensure all dependencies are in package.json
# Verify Docker build works locally:
docker build -t test-build .
```

#### Test Failures
```bash
# Run tests locally first:
cd backend && npm test
cd frontend && npm test

# Check environment variables in CI
# Ensure MongoDB test database is accessible
```

#### Deployment Failures
```bash
# Verify Render secrets are correct
# Check Render service logs
# Ensure environment variables are set on Render
# Verify MongoDB connection string
```

#### Environment Variable Issues
```bash
# Double-check all required variables are set
# Ensure no typos in variable names
# Verify MongoDB URI format and credentials
```

### Health Check Endpoint

The pipeline uses `/api/users` as health check. Ensure this endpoint:
- Returns 200 status for healthy service
- Is accessible without authentication
- Responds quickly (< 10 seconds)

## 7. Security Best Practices

### Secrets Management
- ✅ Never commit secrets to repository
- ✅ Use GitHub Secrets for sensitive data
- ✅ Rotate secrets regularly
- ✅ Use strong, unique passwords

### Database Security
- ✅ Use dedicated database user with minimal permissions
- ✅ Enable authentication on MongoDB
- ✅ Use connection string with SSL
- ✅ Regularly backup your database

### Application Security
- ✅ Use HTTPS in production (Render provides this)
- ✅ Set secure session cookies
- ✅ Implement rate limiting
- ✅ Keep dependencies updated

## 8. Performance Optimization

### Render Service
- Choose appropriate instance size based on traffic
- Enable auto-scaling if needed
- Monitor resource usage

### Database
- Use MongoDB Atlas for better performance
- Implement proper indexing
- Monitor query performance

### Frontend
- Ensure build artifacts are optimized
- Use CDN for static assets if needed
- Implement proper caching headers

## 9. Monitoring and Maintenance

### Regular Tasks
- Monitor application logs on Render
- Check GitHub Actions for failed builds
- Update dependencies regularly
- Monitor database performance

### Alerts Setup
- Set up Render alerts for downtime
- Monitor GitHub Actions notifications
- Set up MongoDB Atlas alerts

## 🎉 You're Ready!

Once you've completed these steps:
1. Create a pull request to test the pipeline
2. Merge to main to deploy
3. Visit your Render URL to see your live application

Your CI/CD pipeline will now automatically test and deploy your application on every push to the main branch!
