# Guide Complet : Déploiement et CI/CD pour Adopte1Etudiant

Ce guide complet vous explique comment configurer le déploiement automatique de votre application sur Render.com avec GitHub Actions.

## 📋 Vue d'ensemble

**Architecture de déploiement :**
- **Frontend** : React (Node.js 16.14.0)
- **Backend** : Express.js API (Node.js 16.13.1)
- **Base de données** : MongoDB Atlas
- **Déploiement** : Render.com avec Docker
- **CI/CD** : GitHub Actions

## 🚀 Checklist de Configuration

- [ ] Créer un compte et service Render.com
- [ ] Configurer les secrets GitHub
- [ ] Configurer les variables d'environnement sur Render
- [ ] Configurer MongoDB Atlas
- [ ] Tester le pipeline de déploiement
- [ ] Vérifier le déploiement en production

---

# PARTIE 1 : Configuration Render.com

## 1.1 Créer un Service Web

### Étapes de création :

1. **Inscription/Connexion** sur [Render.com](https://render.com)
2. **Créer un nouveau Web Service** :
   - Cliquez sur "New +" → "Web Service"
   - Connectez votre repository GitHub
   - Sélectionnez le repository `adopteunetudiant`

### Configuration du Service

```yaml
# Paramètres de base
Name: adopte1etudiant
Environment: Docker
Region: Frankfurt (Europe) ou Oregon (US)
Branch: main

# Build & Deploy
Dockerfile Path: ./Dockerfile
Docker Context Directory: . (racine du projet)

# Détails du service
Port: 3001
Health Check Path: /api/users
Auto-Deploy: Yes (déploiement automatique sur push)
```

### Configuration avancée

```yaml
# Ressources (ajustables selon vos besoins)
Instance Type: Starter (gratuit) ou Standard
CPU: 0.5 CPU
Memory: 512 MB

# Scaling (optionnel)
Auto-scaling: Disabled (pour commencer)
Max instances: 1
```

## 1.2 Variables d'Environnement sur Render

Dans le dashboard de votre service Render, allez dans **Settings > Environment** et ajoutez :

```bash
# Configuration de l'application
NODE_ENV=production
PORT=3001

# Base de données MongoDB Atlas
MONGODB_URI=mongodb+srv://username:<EMAIL>/adopte1etudiant?retryWrites=true&w=majority

# Sécurité et authentification
JWT_SECRET=votre-secret-jwt-32-caracteres-minimum
SESSION_SECRET=votre-secret-session-32-caracteres-minimum
ADMIN_COOKIE_PASSWORD=votre-mot-de-passe-cookie-admin
ADMIN_COOKIE_NAME=admin-bro-cookie

# Configuration administrateur
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=votre-mot-de-passe-admin-securise

# URL du frontend (sera votre URL Render)
FRONT_URL=https://votre-app.onrender.com
```

### Génération de Secrets Sécurisés

Utilisez ces commandes pour générer des secrets forts :

```bash
# Secret JWT (32+ caractères)
openssl rand -base64 32

# Secret de session (32+ caractères)
openssl rand -base64 32

# Mot de passe cookie admin (32+ caractères)
openssl rand -base64 32
```

### ⚠️ Important : Sécurité des Secrets

- **Jamais de secrets dans le code** : Utilisez uniquement les variables d'environnement
- **Secrets forts** : Minimum 32 caractères aléatoires
- **Rotation régulière** : Changez les secrets périodiquement
- **Accès limité** : Seules les personnes autorisées doivent connaître les secrets

---

# PARTIE 2 : Configuration MongoDB Atlas

## 2.1 Création du Cluster MongoDB

### Étapes de configuration :

1. **Créer un compte** sur [MongoDB Atlas](https://www.mongodb.com/cloud/atlas)
2. **Créer un nouveau cluster** :
   - Choisissez le plan gratuit (M0 Sandbox)
   - Région : Europe (Ireland) ou US East
   - Nom du cluster : `Cluster0` (par défaut)

### Configuration de la base de données

```bash
# Nom de la base de données
Database Name: adopte1etudiant

# Collections principales
- users (étudiants et entreprises)
- adoptions
- joboffers
- jobapplications
- contracts
- conversations
- messages
- domains
- searchtypes
```

## 2.2 Configuration de l'Accès

### Créer un utilisateur de base de données :

1. **Database Access** → **Add New Database User**
2. **Authentication Method** : Password
3. **Username** : `adopte1etudiant`
4. **Password** : Générez un mot de passe fort
5. **Database User Privileges** : Read and write to any database

### Configuration réseau :

1. **Network Access** → **Add IP Address**
2. **Access List Entry** : `0.0.0.0/0` (pour Render.com)
3. **Comment** : "Render.com deployment access"

### Obtenir la chaîne de connexion :

1. **Clusters** → **Connect** → **Connect your application**
2. **Driver** : Node.js, Version 4.1 or later
3. **Connection String** :
```
mongodb+srv://adopte1etudiant:<password>@cluster0.xxxxx.mongodb.net/adopte1etudiant?retryWrites=true&w=majority
```

---

# PARTIE 3 : Configuration GitHub Actions (CI/CD)

## 3.1 Secrets GitHub Requis

Allez dans **Settings > Secrets and variables > Actions** de votre repository GitHub :

| Nom du Secret | Description | Comment l'obtenir |
|---------------|-------------|-------------------|
| `RENDER_API_KEY` | Clé API Render | Account Settings > API Keys |
| `RENDER_SERVICE_ID` | ID du service Render | URL du service (srv-xxxxx) |

### Obtenir la clé API Render :

1. Allez sur [Render Dashboard](https://dashboard.render.com)
2. Cliquez sur votre profil (en haut à droite)
3. **Account Settings** → **API Keys**
4. **Create API Key** → Copiez la clé générée

### Obtenir l'ID du service :

1. Allez sur le dashboard de votre service Render
2. L'ID est dans l'URL : `https://dashboard.render.com/web/srv-XXXXXXXXX`
3. Copiez la partie `srv-XXXXXXXXX` (avec le préfixe "srv-")

## 3.2 Fonctionnement du Pipeline CI/CD

### Déclencheurs automatiques :

**Sur Pull Request vers main :**
```
✅ Tests backend (Jest + MongoDB)
✅ Tests frontend (React Testing Library)
✅ Tests E2E (Cypress smoke tests)
✅ Vérification du build
❌ Déploiement (ignoré pour les PRs)
```

**Sur Push vers main :**
```
✅ Tous les tests
✅ Build de l'application
✅ Déploiement sur Render.com
```

### Structure des workflows :

- **`.github/workflows/main.yaml`** : Pipeline principal (tests + déploiement)
- **`.github/workflows/test.yaml`** : Tests uniquement (branches de feature)

---

# PARTIE 4 : Tests et Déploiement

## 4.1 Tests Locaux (Avant Push)

### Script de test automatique :

```bash
# Rendre le script exécutable
chmod +x scripts/test-local.sh

# Lancer tous les tests locaux
./scripts/test-local.sh
```

### Tests manuels :

```bash
# Tests backend
cd backend
npm ci
npm test

# Tests frontend
cd frontend
npm ci
npm test -- --coverage --watchAll=false

# Build complet
npm run build
```

## 4.2 Processus de Déploiement

### Étape 1 : Créer une Pull Request

```bash
# Créer une branche de feature
git checkout -b feature/nouvelle-fonctionnalite

# Faire vos modifications
git add .
git commit -m "feat: ajout nouvelle fonctionnalité"

# Pousser la branche
git push origin feature/nouvelle-fonctionnalite

# Créer une PR vers main sur GitHub
```

**Résultat :** Tests automatiques sans déploiement

### Étape 2 : Merger vers main

```bash
# Après validation de la PR
git checkout main
git pull origin main
```

**Résultat :** Tests + Déploiement automatique sur Render

## 4.3 Surveillance du Déploiement

### Où surveiller :

1. **GitHub Actions** : Progression des workflows
2. **Render Dashboard** : Logs de build et déploiement
3. **Application** : Vérifier l'URL déployée

---

# PARTIE 5 : Dépannage et Maintenance

## 5.1 Problèmes Courants

### Échecs de Build

**Symptômes :** Le build échoue sur GitHub Actions ou Render

**Solutions :**
```bash
# Vérifier la compatibilité Node.js
node --version  # Doit être 16.13.1 (backend) ou 16.14.0 (frontend)

# Tester le build localement
docker build -t test-build .

# Vérifier les dépendances
cd backend && npm audit --audit-level=high
cd frontend && npm audit --audit-level=high
```

### Échecs de Tests

**Symptômes :** Tests qui passent localement mais échouent en CI

**Solutions :**
```bash
# Lancer les tests avec les mêmes conditions que la CI
NODE_ENV=test npm test

# Vérifier la base de données de test
MONGODB_URI=mongodb://localhost:27017/test npm test

# Nettoyer les caches
npm ci  # Au lieu de npm install
```

### Échecs de Déploiement

**Symptômes :** Tests passent mais déploiement échoue

**Solutions :**
1. **Vérifier les secrets GitHub** :
   - `RENDER_API_KEY` correct et valide
   - `RENDER_SERVICE_ID` avec préfixe "srv-"

2. **Vérifier les variables Render** :
   - Toutes les variables d'environnement définies
   - `MONGODB_URI` accessible depuis Render
   - Pas de typos dans les noms de variables

3. **Vérifier les logs Render** :
   - Dashboard Render > Service > Logs
   - Chercher les erreurs de connexion DB ou variables manquantes

### Problèmes de Variables d'Environnement

**Symptômes :** Erreurs de connexion DB ou authentification

**Solutions :**
```bash
# Vérifier le format de l'URI MongoDB
mongodb+srv://username:<EMAIL>/dbname?retryWrites=true&w=majority

# Tester la connexion depuis un autre outil
mongosh "mongodb+srv://cluster.mongodb.net/adopte1etudiant" --username adopte1etudiant

# Vérifier les caractères spéciaux dans le mot de passe
# Encoder les caractères spéciaux : @ = %40, : = %3A, etc.
```

### Problèmes de Health Check

**Endpoint utilisé :** `/api/users`

**Vérifications :**
- Retourne un statut 200 pour un service sain
- Accessible sans authentification
- Répond en moins de 10 secondes
- Disponible après le démarrage de l'application

## 5.2 Surveillance et Monitoring

### Métriques à surveiller :

1. **Performance de l'application** :
   - Temps de réponse des API
   - Utilisation CPU/RAM sur Render
   - Temps de build et déploiement

2. **Santé de la base de données** :
   - Connexions actives MongoDB
   - Temps de réponse des requêtes
   - Espace de stockage utilisé

3. **Pipeline CI/CD** :
   - Taux de succès des tests
   - Durée des workflows
   - Fréquence des déploiements

### Outils de monitoring :

```bash
# Logs Render en temps réel
curl -H "Authorization: Bearer $RENDER_API_KEY" \
  https://api.render.com/v1/services/$SERVICE_ID/logs

# Monitoring MongoDB Atlas
# Dashboard Atlas > Metrics > Real Time

# GitHub Actions insights
# Repository > Insights > Actions
```

---

# PARTIE 6 : Sécurité et Bonnes Pratiques

## 6.1 Gestion des Secrets

### ✅ Bonnes pratiques :

- **Jamais de secrets dans le code** : Utilisez uniquement les variables d'environnement
- **GitHub Secrets** : Pour les clés API et identifiants sensibles
- **Rotation régulière** : Changez les secrets tous les 3-6 mois
- **Mots de passe forts** : Minimum 32 caractères aléatoires

### ✅ Sécurité de la base de données :

- **Utilisateur dédié** : Permissions minimales nécessaires
- **Authentification activée** : Toujours sur MongoDB Atlas
- **Connexion SSL** : Incluse dans l'URI MongoDB Atlas
- **Sauvegardes régulières** : Automatiques sur Atlas

### ✅ Sécurité de l'application :

- **HTTPS en production** : Fourni automatiquement par Render
- **Cookies sécurisés** : `secure: true` en production
- **Rate limiting** : Implémenté dans l'application
- **Dépendances à jour** : `npm audit` dans le pipeline

## 6.2 Optimisation des Performances

### Configuration Render recommandée :

```yaml
# Pour une application en production
Instance Type: Standard
CPU: 1 vCPU
Memory: 1 GB
Auto-scaling: Enabled (si trafic variable)
```

### Optimisations base de données :

```javascript
// Index recommandés pour MongoDB
db.users.createIndex({ email: 1 }, { unique: true })
db.joboffers.createIndex({ company: 1, createdAt: -1 })
db.jobapplications.createIndex({ jobOffer: 1, student: 1 })
db.adoptions.createIndex({ company: 1, student: 1 })
```

### Optimisations frontend :

```bash
# Build optimisé pour la production
npm run build

# Vérifier la taille du bundle
du -sh frontend/build/

# Analyser le bundle (optionnel)
npx webpack-bundle-analyzer frontend/build/static/js/*.js
```

---

# PARTIE 7 : Mise en Production

## 7.1 Checklist de Déploiement Final

### Avant le premier déploiement :

- [ ] **Service Render créé** et configuré
- [ ] **Variables d'environnement** définies sur Render
- [ ] **MongoDB Atlas** configuré et accessible
- [ ] **Secrets GitHub** ajoutés (RENDER_API_KEY, RENDER_SERVICE_ID)
- [ ] **Tests locaux** passent avec `./scripts/test-local.sh`
- [ ] **Build Docker** fonctionne localement
- [ ] **Health check** `/api/users` accessible

### Processus de déploiement :

```bash
# 1. Tester localement
./scripts/test-local.sh

# 2. Créer une branche de test
git checkout -b test-deployment
git push origin test-deployment

# 3. Créer une PR vers main
# → Vérifier que tous les tests passent

# 4. Merger vers main
# → Déploiement automatique sur Render

# 5. Vérifier le déploiement
curl https://votre-app.onrender.com/api/users
```

## 7.2 Post-Déploiement

### Vérifications immédiates :

1. **Application accessible** : `https://votre-app.onrender.com`
2. **API fonctionnelle** : `/api/users` retourne 200
3. **Base de données connectée** : Pas d'erreurs de connexion
4. **Authentification** : Login/logout fonctionnent
5. **Upload de fichiers** : CV et photos fonctionnent

### Configuration du domaine personnalisé (optionnel) :

```bash
# Sur Render Dashboard > Settings > Custom Domains
# Ajouter : adopte1etudiant.com
# Configurer DNS : CNAME vers votre-app.onrender.com
# SSL automatique avec Let's Encrypt
```

## 7.3 Maintenance Continue

### Tâches hebdomadaires :

- Vérifier les logs d'erreur sur Render
- Surveiller les métriques de performance
- Vérifier les sauvegardes MongoDB Atlas

### Tâches mensuelles :

- Mettre à jour les dépendances (`npm update`)
- Vérifier les vulnérabilités (`npm audit`)
- Analyser les performances de la base de données
- Réviser les logs d'accès et d'erreur

### Tâches trimestrielles :

- Rotation des secrets (JWT, sessions, admin)
- Révision des permissions MongoDB
- Optimisation des requêtes lentes
- Mise à jour des versions Node.js si nécessaire

---

# 🎉 Félicitations !

Votre pipeline CI/CD est maintenant configuré et opérationnel !

## 📋 Récapitulatif

✅ **Render.com** : Service web configuré avec Docker
✅ **MongoDB Atlas** : Base de données cloud sécurisée
✅ **GitHub Actions** : Pipeline CI/CD automatisé
✅ **Tests automatiques** : Backend, Frontend, E2E
✅ **Déploiement automatique** : Sur push vers main
✅ **Monitoring** : Logs et métriques disponibles

## 🚀 Workflow de Développement

```
Feature Branch → Pull Request → Tests → Review → Merge → Deploy
```

Votre application se déploie automatiquement à chaque merge vers `main` ! 🎊
