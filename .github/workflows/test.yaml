name: Test Suite

on:
  pull_request:
    branches: [ main, develop ]
  push:
    branches: [ develop, 'feature/*', 'bugfix/*', 'hotfix/*' ]

env:
  NODE_VERSION: '16.13.1'
  FRONTEND_NODE_VERSION: '16.14.0'

jobs:
  # Quick test job for feature branches
  test-backend-quick:
    name: Backend Unit Tests
    runs-on: ubuntu-latest
    
    services:
      mongodb:
        image: mongo:4.4
        ports:
          - 27017:27017
        options: >-
          --health-cmd "mongo --eval 'db.adminCommand(\"ismaster\")'"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: backend/package-lock.json

      - name: Install dependencies
        run: |
          cd backend
          npm ci

      - name: Run unit tests
        run: |
          cd backend
          npm test -- --testPathPattern="unit"
        env:
          NODE_ENV: test
          MONGODB_URI: mongodb://localhost:27017/adopte-etudiant-test
          JWT_SECRET: test-jwt-secret-key-for-ci
          SESSION_SECRET: test-session-secret-key-for-ci
          ADMIN_COOKIE_PASSWORD: test-admin-cookie-password
          ADMIN_EMAIL: <EMAIL>
          ADMIN_PASSWORD: testpassword
          FRONT_URL: http://localhost:3000

  test-frontend-quick:
    name: Frontend Unit Tests
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.FRONTEND_NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Install dependencies
        run: |
          cd frontend
          npm ci

      - name: Run unit tests
        run: |
          cd frontend
          npm test -- --coverage --watchAll=false --testTimeout=10000
        env:
          CI: true

      - name: Upload coverage reports
        uses: actions/upload-artifact@v4
        with:
          name: frontend-coverage
          path: frontend/coverage/
          retention-days: 7

  # Lint and code quality checks
  lint-and-quality:
    name: Code Quality
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: backend/package-lock.json

      - name: Install backend dependencies
        run: |
          cd backend
          npm ci

      - name: Check for security vulnerabilities
        run: |
          cd backend
          npm audit --audit-level=high

      - name: Setup Node.js for frontend
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.FRONTEND_NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Install frontend dependencies
        run: |
          cd frontend
          npm ci

      - name: Check frontend security
        run: |
          cd frontend
          npm audit --audit-level=high

  # Build verification
  build-check:
    name: Build Verification
    runs-on: ubuntu-latest
    needs: [test-backend-quick, test-frontend-quick]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: package-lock.json

      - name: Build application
        run: npm run build

      - name: Verify build output
        run: |
          echo "✅ Build completed successfully"
          echo "📦 Frontend build size:"
          du -sh frontend/build/
          echo "📁 Build contents:"
          ls -la frontend/build/

  # Summary job
  test-summary:
    name: Test Summary
    runs-on: ubuntu-latest
    needs: [test-backend-quick, test-frontend-quick, lint-and-quality, build-check]
    if: always()

    steps:
      - name: Test Results Summary
        run: |
          echo "## 📊 Test Results Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          if [ "${{ needs.test-backend-quick.result }}" == "success" ]; then
            echo "✅ Backend Tests: PASSED" >> $GITHUB_STEP_SUMMARY
          else
            echo "❌ Backend Tests: FAILED" >> $GITHUB_STEP_SUMMARY
          fi
          
          if [ "${{ needs.test-frontend-quick.result }}" == "success" ]; then
            echo "✅ Frontend Tests: PASSED" >> $GITHUB_STEP_SUMMARY
          else
            echo "❌ Frontend Tests: FAILED" >> $GITHUB_STEP_SUMMARY
          fi
          
          if [ "${{ needs.lint-and-quality.result }}" == "success" ]; then
            echo "✅ Code Quality: PASSED" >> $GITHUB_STEP_SUMMARY
          else
            echo "❌ Code Quality: FAILED" >> $GITHUB_STEP_SUMMARY
          fi
          
          if [ "${{ needs.build-check.result }}" == "success" ]; then
            echo "✅ Build Check: PASSED" >> $GITHUB_STEP_SUMMARY
          else
            echo "❌ Build Check: FAILED" >> $GITHUB_STEP_SUMMARY
          fi
          
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "🔗 **Next Steps:**" >> $GITHUB_STEP_SUMMARY
          echo "- If all tests pass, your PR is ready for review" >> $GITHUB_STEP_SUMMARY
          echo "- Merge to main branch will trigger deployment to Render.com" >> $GITHUB_STEP_SUMMARY
