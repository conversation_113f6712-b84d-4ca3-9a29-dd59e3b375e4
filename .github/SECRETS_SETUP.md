# GitHub Secrets Setup Guide

This file contains the exact secrets you need to configure in your GitHub repository for the CI/CD pipeline to work with Render.com.

## 🔐 Required GitHub Secrets

Go to **Settings > Secrets and variables > Actions** in your GitHub repository and add these secrets:

### 1. RENDER_API_KEY
- **Description**: Your Render.com API key for deployment
- **How to get**: 
  1. Go to [Render Dashboard](https://dashboard.render.com)
  2. Click your profile (top right) → Account Settings
  3. Go to API Keys tab
  4. Click "Create API Key"
  5. Copy the generated key

### 2. RENDER_SERVICE_ID
- **Description**: Your Render service ID
- **How to get**:
  1. Go to your service dashboard on Render
  2. Look at the URL: `https://dashboard.render.com/web/srv-XXXXXXXXX`
  3. Copy the `srv-XXXXXXXXX` part (including the "srv-" prefix)

## 🌍 Environment Variables on Render

Set these in your Render service dashboard (Settings > Environment):

```bash
# Required for production
NODE_ENV=production
PORT=3001

# Database (replace with your MongoDB Atlas connection string)
MONGODB_URI=mongodb+srv://username:<EMAIL>/adopte1etudiant?retryWrites=true&w=majority

# Security (generate with: openssl rand -base64 32)
JWT_SECRET=your-32-char-jwt-secret-here
SESSION_SECRET=your-32-char-session-secret-here
ADMIN_COOKIE_PASSWORD=your-32-char-cookie-password-here

# Admin access
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=your-secure-admin-password
ADMIN_COOKIE_NAME=admin-bro-cookie

# Frontend URL (replace with your actual Render URL)
FRONT_URL=https://your-app-name.onrender.com
```

## 🔧 Generate Secure Secrets

Use these commands to generate secure secrets:

```bash
# Generate JWT Secret
openssl rand -base64 32

# Generate Session Secret  
openssl rand -base64 32

# Generate Admin Cookie Password
openssl rand -base64 32
```

## ✅ Verification Checklist

Before pushing to main:

- [ ] GitHub secrets configured (RENDER_API_KEY, RENDER_SERVICE_ID)
- [ ] Render service created and configured
- [ ] Environment variables set on Render
- [ ] MongoDB database accessible from Render
- [ ] All secrets are 32+ characters long
- [ ] FRONT_URL matches your Render service URL

## 🚀 Testing the Setup

1. **Create a feature branch**: `git checkout -b test-ci-cd`
2. **Make a small change** and commit it
3. **Push the branch**: `git push origin test-ci-cd`
4. **Create a Pull Request** to main
5. **Check GitHub Actions** tab - tests should run but deployment should be skipped
6. **Merge the PR** - this will trigger deployment to Render

## 🔍 Troubleshooting

### Common Issues:

**"Invalid API key"**
- Verify RENDER_API_KEY is correct
- Ensure no extra spaces in the secret

**"Service not found"**
- Verify RENDER_SERVICE_ID includes "srv-" prefix
- Check the service exists and you have access

**"Environment variable missing"**
- Ensure all required variables are set on Render
- Check for typos in variable names

**"Database connection failed"**
- Verify MongoDB URI is correct
- Check MongoDB Atlas IP whitelist (use 0.0.0.0/0 for Render)
- Ensure database user has proper permissions

## 📞 Support

If you encounter issues:
1. Check GitHub Actions logs for detailed error messages
2. Check Render deployment logs in the dashboard
3. Verify all environment variables are set correctly
4. Test database connection separately
