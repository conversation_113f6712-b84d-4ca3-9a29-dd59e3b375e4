/*!
 * Font Awesome Free 6.0.0-beta3 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)
 * Copyright 2021 Fonticons, Inc.
 */
!function(){"use strict";function n(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),a.push.apply(a,n)}return a}function I(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?n(Object(a),!0).forEach(function(t){o(e,t,a[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):n(Object(a)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))})}return e}function r(t){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function i(t,e){for(var a=0;a<e.length;a++){var n=e[a];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function o(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}function v(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var a=t&&("undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"]);if(null==a)return;var n,i,r=[],o=!0,c=!1;try{for(a=a.call(t);!(o=(n=a.next()).done)&&(r.push(n.value),!e||r.length!==e);o=!0);}catch(t){c=!0,i=t}finally{try{o||null==a.return||a.return()}finally{if(c)throw i}}return r}(t,e)||a(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function m(t){return function(t){if(Array.isArray(t))return c(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||a(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function a(t,e){if(t){if("string"==typeof t)return c(t,e);var a=Object.prototype.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?c(t,e):void 0}}function c(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,n=new Array(e);a<e;a++)n[a]=t[a];return n}var t=function(){},e={},s={},f=null,l={mark:t,measure:t};try{"undefined"!=typeof window&&(e=window),"undefined"!=typeof document&&(s=document),"undefined"!=typeof MutationObserver&&(f=MutationObserver),"undefined"!=typeof performance&&(l=performance)}catch(t){}var u=(e.navigator||{}).userAgent,d=void 0===u?"":u,L=e,R=s,p=f,g=l,b=!!L.document,h=!!R.documentElement&&!!R.head&&"function"==typeof R.addEventListener&&"function"==typeof R.createElement,x=~d.indexOf("MSIE")||~d.indexOf("Trident/"),y="___FONT_AWESOME___",A=16,k="fa",w="svg-inline--fa",j="data-fa-i2svg",T="data-fa-pseudo-element",O="data-fa-pseudo-element-pending",D="data-prefix",F="data-icon",N="fontawesome-i2svg",C="async",P=["HTML","HEAD","STYLE","SCRIPT"],S=function(){try{return!0}catch(t){return!1}}(),E={fas:"solid","fa-solid":"solid",far:"regular","fa-regular":"regular",fal:"light","fa-light":"light",fat:"thin","fa-thin":"thin",fad:"duotone","fa-duotone":"duotone",fab:"brands","fa-brands":"brands",fak:"kit","fa-kit":"kit",fa:"solid"},Y={solid:"fas",regular:"far",light:"fal",thin:"fat",duotone:"fad",brands:"fab",kit:"fak"},z={fab:"fa-brands",fad:"fa-duotone",fak:"fa-kit",fal:"fa-light",far:"fa-regular",fas:"fa-solid",fat:"fa-thin"},M=Object.fromEntries(Object.entries(z).map(function(t){return[t[1],t[0]]})),H=/fa[srltdbk\-\ ]/,W="fa-layers-text",U=/Font ?Awesome ?([56 ]*)(Solid|Regular|Light|Thin|Duotone|Brands|Free|Pro|Kit)?.*/i,_={900:"fas",400:"far",normal:"far",300:"fal",100:"fat"},B=[1,2,3,4,5,6,7,8,9,10],X=B.concat([11,12,13,14,15,16,17,18,19,20]),q=["class","data-prefix","data-icon","data-fa-transform","data-fa-mask"],V={GROUP:"duotone-group",SWAP_OPACITY:"swap-opacity",PRIMARY:"primary",SECONDARY:"secondary"},G=[].concat(m(Object.keys(Y)),["2xs","xs","sm","lg","xl","2xl","beat","border","fade","beat-fade","flip-both","flip-horizontal","flip-vertical","flip","fw","inverse","layers-counter","layers-text","layers","li","pull-left","pull-right","pulse","rotate-180","rotate-270","rotate-90","rotate-by","spin-pulse","spin-reverse","spin","stack-1x","stack-2x","stack","ul",V.GROUP,V.SWAP_OPACITY,V.PRIMARY,V.SECONDARY]).concat(B.map(function(t){return"".concat(t,"x")})).concat(X.map(function(t){return"w-".concat(t)})),K=L.FontAwesomeConfig||{};if(R&&"function"==typeof R.querySelector){[["data-family-prefix","familyPrefix"],["data-style-default","styleDefault"],["data-replacement-class","replacementClass"],["data-auto-replace-svg","autoReplaceSvg"],["data-auto-add-css","autoAddCss"],["data-auto-a11y","autoA11y"],["data-search-pseudo-elements","searchPseudoElements"],["data-observe-mutations","observeMutations"],["data-mutate-approach","mutateApproach"],["data-keep-original-source","keepOriginalSource"],["data-measure-performance","measurePerformance"],["data-show-missing-icons","showMissingIcons"]].forEach(function(t){var e,a=v(t,2),n=a[0],i=a[1],r=""===(e=function(t){var e=R.querySelector("script["+t+"]");if(e)return e.getAttribute(t)}(n))||"false"!==e&&("true"===e||e);null!=r&&(K[i]=r)})}var J=I(I({},{familyPrefix:k,styleDefault:"solid",replacementClass:w,autoReplaceSvg:!0,autoAddCss:!0,autoA11y:!0,searchPseudoElements:!1,observeMutations:!0,mutateApproach:"async",keepOriginalSource:!0,measurePerformance:!1,showMissingIcons:!0}),K);J.autoReplaceSvg||(J.observeMutations=!1);var Q={};Object.keys(J).forEach(function(e){Object.defineProperty(Q,e,{enumerable:!0,set:function(t){J[e]=t,Z.forEach(function(t){return t(Q)})},get:function(){return J[e]}})}),L.FontAwesomeConfig=Q;var Z=[];var $=A,tt={size:16,x:0,y:0,rotate:0,flipX:!1,flipY:!1};var et="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";function at(){for(var t=12,e="";0<t--;)e+=et[62*Math.random()|0];return e}function nt(t){for(var e=[],a=(t||[]).length>>>0;a--;)e[a]=t[a];return e}function it(t){return t.classList?nt(t.classList):(t.getAttribute("class")||"").split(" ").filter(function(t){return t})}function rt(t){return"".concat(t).replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function ot(a){return Object.keys(a||{}).reduce(function(t,e){return t+"".concat(e,": ").concat(a[e].trim(),";")},"")}function ct(t){return t.size!==tt.size||t.x!==tt.x||t.y!==tt.y||t.rotate!==tt.rotate||t.flipX||t.flipY}var st=':host,:root{--fa-font-solid:normal 900 1em/1 "Font Awesome 6 Solid";--fa-font-regular:normal 400 1em/1 "Font Awesome 6 Regular";--fa-font-light:normal 300 1em/1 "Font Awesome 6 Light";--fa-font-thin:normal 100 1em/1 "Font Awesome 6 Thin";--fa-font-duotone:normal 900 1em/1 "Font Awesome 6 Duotone";--fa-font-brands:normal 400 1em/1 "Font Awesome 6 Brands"}svg:not(:host).svg-inline--fa,svg:not(:root).svg-inline--fa{overflow:visible;-webkit-box-sizing:content-box;box-sizing:content-box}.svg-inline--fa{display:var(--fa-display,inline-block);height:1em;overflow:visible;vertical-align:-.125em}.svg-inline--fa.fa-2xs{vertical-align:.1em}.svg-inline--fa.fa-xs{vertical-align:0}.svg-inline--fa.fa-sm{vertical-align:-.0714285705em}.svg-inline--fa.fa-lg{vertical-align:-.2em}.svg-inline--fa.fa-xl{vertical-align:-.25em}.svg-inline--fa.fa-2xl{vertical-align:-.3125em}.svg-inline--fa.fa-pull-left{margin-right:var(--fa-pull-margin,.3em);width:auto}.svg-inline--fa.fa-pull-right{margin-left:var(--fa-pull-margin,.3em);width:auto}.svg-inline--fa.fa-li{width:var(--fa-li-width,2em);top:.25em}.svg-inline--fa.fa-fw{width:var(--fa-fw-width,1.25em)}.fa-layers svg.svg-inline--fa{bottom:0;left:0;margin:auto;position:absolute;right:0;top:0}.fa-layers-counter,.fa-layers-text{display:inline-block;position:absolute;text-align:center}.fa-layers{display:inline-block;height:1em;position:relative;text-align:center;vertical-align:-.125em;width:1em}.fa-layers svg.svg-inline--fa{-webkit-transform-origin:center center;transform-origin:center center}.fa-layers-text{left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);-webkit-transform-origin:center center;transform-origin:center center}.fa-layers-counter{background-color:var(--fa-counter-background-color,#ff253a);border-radius:var(--fa-counter-border-radius,1em);-webkit-box-sizing:border-box;box-sizing:border-box;color:var(--fa-inverse,#fff);line-height:var(--fa-counter-line-height,1);max-width:var(--fa-counter-max-width,5em);min-width:var(--fa-counter-min-width,1.5em);overflow:hidden;padding:var(--fa-counter-padding,.25em .5em);right:var(--fa-right,0);text-overflow:ellipsis;top:var(--fa-top,0);-webkit-transform:scale(var(--fa-counter-scale,.25));transform:scale(var(--fa-counter-scale,.25));-webkit-transform-origin:top right;transform-origin:top right}.fa-layers-bottom-right{bottom:var(--fa-bottom,0);right:var(--fa-right,0);top:auto;-webkit-transform:scale(var(--fa-layers-scale,.25));transform:scale(var(--fa-layers-scale,.25));-webkit-transform-origin:bottom right;transform-origin:bottom right}.fa-layers-bottom-left{bottom:var(--fa-bottom,0);left:var(--fa-left,0);right:auto;top:auto;-webkit-transform:scale(var(--fa-layers-scale,.25));transform:scale(var(--fa-layers-scale,.25));-webkit-transform-origin:bottom left;transform-origin:bottom left}.fa-layers-top-right{top:var(--fa-top,0);right:var(--fa-right,0);-webkit-transform:scale(var(--fa-layers-scale,.25));transform:scale(var(--fa-layers-scale,.25));-webkit-transform-origin:top right;transform-origin:top right}.fa-layers-top-left{left:var(--fa-left,0);right:auto;top:var(--fa-top,0);-webkit-transform:scale(var(--fa-layers-scale,.25));transform:scale(var(--fa-layers-scale,.25));-webkit-transform-origin:top left;transform-origin:top left}.fa-1x{font-size:1em}.fa-2x{font-size:2em}.fa-3x{font-size:3em}.fa-4x{font-size:4em}.fa-5x{font-size:5em}.fa-6x{font-size:6em}.fa-7x{font-size:7em}.fa-8x{font-size:8em}.fa-9x{font-size:9em}.fa-10x{font-size:10em}.fa-2xs{font-size:.625em;line-height:.1em;vertical-align:.225em}.fa-xs{font-size:.75em;line-height:.0833333337em;vertical-align:.125em}.fa-sm{font-size:.875em;line-height:.0714285718em;vertical-align:.0535714295em}.fa-lg{font-size:1.25em;line-height:.05em;vertical-align:-.075em}.fa-xl{font-size:1.5em;line-height:.0416666682em;vertical-align:-.125em}.fa-2xl{font-size:2em;line-height:.03125em;vertical-align:-.1875em}.fa-fw{text-align:center;width:1.25em}.fa-ul{list-style-type:none;margin-left:var(--fa-li-margin,2.5em);padding-left:0}.fa-ul>li{position:relative}.fa-li{left:calc(var(--fa-li-width,2em) * -1);position:absolute;text-align:center;width:var(--fa-li-width,2em);line-height:inherit}.fa-border{border-color:var(--fa-border-color,#eee);border-radius:var(--fa-border-radius,.1em);border-style:var(--fa-border-style,solid);border-width:var(--fa-border-width,.08em);padding:var(--fa-border-padding,.2em .25em .15em)}.fa-pull-left{float:left;margin-right:var(--fa-pull-margin,.3em)}.fa-pull-right{float:right;margin-left:var(--fa-pull-margin,.3em)}.fa-beat{-webkit-animation-name:fa-beat;animation-name:fa-beat;-webkit-animation-delay:var(--fa-animation-delay,0);animation-delay:var(--fa-animation-delay,0);-webkit-animation-direction:var(--fa-animation-direction,normal);animation-direction:var(--fa-animation-direction,normal);-webkit-animation-duration:var(--fa-animation-duration,1s);animation-duration:var(--fa-animation-duration,1s);-webkit-animation-iteration-count:var(--fa-animation-iteration-count,infinite);animation-iteration-count:var(--fa-animation-iteration-count,infinite);-webkit-animation-timing-function:var(--fa-animation-timing,ease-in-out);animation-timing-function:var(--fa-animation-timing,ease-in-out)}.fa-fade{-webkit-animation-name:fa-fade;animation-name:fa-fade;-webkit-animation-delay:var(--fa-animation-delay,0);animation-delay:var(--fa-animation-delay,0);-webkit-animation-direction:var(--fa-animation-direction,normal);animation-direction:var(--fa-animation-direction,normal);-webkit-animation-duration:var(--fa-animation-duration,1s);animation-duration:var(--fa-animation-duration,1s);-webkit-animation-iteration-count:var(--fa-animation-iteration-count,infinite);animation-iteration-count:var(--fa-animation-iteration-count,infinite);-webkit-animation-timing-function:var(--fa-animation-timing,cubic-bezier(.4,0,.6,1));animation-timing-function:var(--fa-animation-timing,cubic-bezier(.4,0,.6,1))}.fa-beat-fade{-webkit-animation-name:fa-beat-fade;animation-name:fa-beat-fade;-webkit-animation-delay:var(--fa-animation-delay,0);animation-delay:var(--fa-animation-delay,0);-webkit-animation-direction:var(--fa-animation-direction,normal);animation-direction:var(--fa-animation-direction,normal);-webkit-animation-duration:var(--fa-animation-duration,1s);animation-duration:var(--fa-animation-duration,1s);-webkit-animation-iteration-count:var(--fa-animation-iteration-count,infinite);animation-iteration-count:var(--fa-animation-iteration-count,infinite);-webkit-animation-timing-function:var(--fa-animation-timing,cubic-bezier(.4,0,.6,1));animation-timing-function:var(--fa-animation-timing,cubic-bezier(.4,0,.6,1))}.fa-flip{-webkit-animation-name:fa-flip;animation-name:fa-flip;-webkit-animation-delay:var(--fa-animation-delay,0);animation-delay:var(--fa-animation-delay,0);-webkit-animation-direction:var(--fa-animation-direction,normal);animation-direction:var(--fa-animation-direction,normal);-webkit-animation-duration:var(--fa-animation-duration,1s);animation-duration:var(--fa-animation-duration,1s);-webkit-animation-iteration-count:var(--fa-animation-iteration-count,infinite);animation-iteration-count:var(--fa-animation-iteration-count,infinite);-webkit-animation-timing-function:var(--fa-animation-timing,ease-in-out);animation-timing-function:var(--fa-animation-timing,ease-in-out)}.fa-spin{-webkit-animation-name:fa-spin;animation-name:fa-spin;-webkit-animation-delay:var(--fa-animation-delay,0);animation-delay:var(--fa-animation-delay,0);-webkit-animation-direction:var(--fa-animation-direction,normal);animation-direction:var(--fa-animation-direction,normal);-webkit-animation-duration:var(--fa-animation-duration,2s);animation-duration:var(--fa-animation-duration,2s);-webkit-animation-iteration-count:var(--fa-animation-iteration-count,infinite);animation-iteration-count:var(--fa-animation-iteration-count,infinite);-webkit-animation-timing-function:var(--fa-animation-timing,linear);animation-timing-function:var(--fa-animation-timing,linear)}.fa-spin-reverse{--fa-animation-direction:reverse}.fa-pulse,.fa-spin-pulse{-webkit-animation-name:fa-spin;animation-name:fa-spin;-webkit-animation-direction:var(--fa-animation-direction,normal);animation-direction:var(--fa-animation-direction,normal);-webkit-animation-duration:var(--fa-animation-duration,1s);animation-duration:var(--fa-animation-duration,1s);-webkit-animation-iteration-count:var(--fa-animation-iteration-count,infinite);animation-iteration-count:var(--fa-animation-iteration-count,infinite);-webkit-animation-timing-function:var(--fa-animation-timing,steps(8));animation-timing-function:var(--fa-animation-timing,steps(8))}@media (prefers-reduced-motion:reduce){.fa-beat,.fa-beat-fade,.fa-fade,.fa-flip,.fa-pulse,.fa-spin,.fa-spin-pulse{-webkit-animation-delay:-1ms;animation-delay:-1ms;-webkit-animation-duration:1ms;animation-duration:1ms;-webkit-animation-iteration-count:1;animation-iteration-count:1;-webkit-transition-delay:0s;transition-delay:0s;-webkit-transition-duration:0s;transition-duration:0s}}@-webkit-keyframes fa-beat{0%,90%{-webkit-transform:scale(1);transform:scale(1)}45%{-webkit-transform:scale(var(--fa-beat-scale,1.25));transform:scale(var(--fa-beat-scale,1.25))}}@keyframes fa-beat{0%,90%{-webkit-transform:scale(1);transform:scale(1)}45%{-webkit-transform:scale(var(--fa-beat-scale,1.25));transform:scale(var(--fa-beat-scale,1.25))}}@-webkit-keyframes fa-fade{50%{opacity:var(--fa-fade-opacity,.4)}}@keyframes fa-fade{50%{opacity:var(--fa-fade-opacity,.4)}}@-webkit-keyframes fa-beat-fade{0%,100%{opacity:var(--fa-beat-fade-opacity,.4);-webkit-transform:scale(1);transform:scale(1)}50%{opacity:1;-webkit-transform:scale(var(--fa-beat-fade-scale,1.125));transform:scale(var(--fa-beat-fade-scale,1.125))}}@keyframes fa-beat-fade{0%,100%{opacity:var(--fa-beat-fade-opacity,.4);-webkit-transform:scale(1);transform:scale(1)}50%{opacity:1;-webkit-transform:scale(var(--fa-beat-fade-scale,1.125));transform:scale(var(--fa-beat-fade-scale,1.125))}}@-webkit-keyframes fa-flip{50%{-webkit-transform:rotate3d(var(--fa-flip-x,0),var(--fa-flip-y,1),var(--fa-flip-z,0),var(--fa-flip-angle,-180deg));transform:rotate3d(var(--fa-flip-x,0),var(--fa-flip-y,1),var(--fa-flip-z,0),var(--fa-flip-angle,-180deg))}}@keyframes fa-flip{50%{-webkit-transform:rotate3d(var(--fa-flip-x,0),var(--fa-flip-y,1),var(--fa-flip-z,0),var(--fa-flip-angle,-180deg));transform:rotate3d(var(--fa-flip-x,0),var(--fa-flip-y,1),var(--fa-flip-z,0),var(--fa-flip-angle,-180deg))}}@-webkit-keyframes fa-spin{0%{-webkit-transform:rotate(0);transform:rotate(0)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes fa-spin{0%{-webkit-transform:rotate(0);transform:rotate(0)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}.fa-rotate-90{-webkit-transform:rotate(90deg);transform:rotate(90deg)}.fa-rotate-180{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.fa-rotate-270{-webkit-transform:rotate(270deg);transform:rotate(270deg)}.fa-flip-horizontal{-webkit-transform:scale(-1,1);transform:scale(-1,1)}.fa-flip-vertical{-webkit-transform:scale(1,-1);transform:scale(1,-1)}.fa-flip-both,.fa-flip-horizontal.fa-flip-vertical{-webkit-transform:scale(-1,-1);transform:scale(-1,-1)}.fa-rotate-by{-webkit-transform:rotate(var(--fa-rotate-angle,none));transform:rotate(var(--fa-rotate-angle,none))}.fa-stack{display:inline-block;vertical-align:middle;height:2em;position:relative;width:2.5em}.fa-stack-1x,.fa-stack-2x{bottom:0;left:0;margin:auto;position:absolute;right:0;top:0;z-index:var(--fa-stack-z-index,auto)}.svg-inline--fa.fa-stack-1x{height:1em;width:1.25em}.svg-inline--fa.fa-stack-2x{height:2em;width:2.5em}.fa-inverse{color:var(--fa-inverse,#fff)}.fa-sr-only,.sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border-width:0}.fa-sr-only-focusable:not(:focus),.sr-only-focusable:not(:focus){position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border-width:0}.svg-inline--fa .fa-primary{fill:var(--fa-primary-color,currentColor);opacity:var(--fa-primary-opacity,1)}.svg-inline--fa .fa-secondary{fill:var(--fa-secondary-color,currentColor);opacity:var(--fa-secondary-opacity,.4)}.svg-inline--fa.fa-swap-opacity .fa-primary{opacity:var(--fa-secondary-opacity,.4)}.svg-inline--fa.fa-swap-opacity .fa-secondary{opacity:var(--fa-primary-opacity,1)}.svg-inline--fa mask .fa-primary,.svg-inline--fa mask .fa-secondary{fill:#000}.fa-duotone.fa-inverse,.fad.fa-inverse{color:var(--fa-inverse,#fff)}';function ft(){var t=k,e=w,a=Q.familyPrefix,n=Q.replacementClass,i=st;if(a!==t||n!==e){var r=new RegExp("\\.".concat(t,"\\-"),"g"),o=new RegExp("\\--".concat(t,"\\-"),"g"),c=new RegExp("\\.".concat(e),"g");i=i.replace(r,".".concat(a,"-")).replace(o,"--".concat(a,"-")).replace(c,".".concat(n))}return i}var lt=!1;function ut(){Q.autoAddCss&&!lt&&(!function(t){if(t&&h){var e=R.createElement("style");e.setAttribute("type","text/css"),e.innerHTML=t;for(var a=R.head.childNodes,n=null,i=a.length-1;-1<i;i--){var r=a[i],o=(r.tagName||"").toUpperCase();-1<["STYLE","LINK"].indexOf(o)&&(n=r)}R.head.insertBefore(e,n)}}(ft()),lt=!0)}var mt={mixout:function(){return{dom:{css:ft,insertCss:ut}}},hooks:function(){return{beforeDOMElementCreation:function(){ut()},beforeI2svg:function(){ut()}}}},dt=L||{};dt[y]||(dt[y]={}),dt[y].styles||(dt[y].styles={}),dt[y].hooks||(dt[y].hooks={}),dt[y].shims||(dt[y].shims=[]);var vt=dt[y],pt=[],gt=!1;function bt(t){h&&(gt?setTimeout(t,0):pt.push(t))}function ht(t){var a,e=t.tag,n=t.attributes,i=void 0===n?{}:n,r=t.children,o=void 0===r?[]:r;return"string"==typeof t?rt(t):"<".concat(e," ").concat((a=i,Object.keys(a||{}).reduce(function(t,e){return t+"".concat(e,'="').concat(rt(a[e]),'" ')},"").trim()),">").concat(o.map(ht).join(""),"</").concat(e,">")}function yt(t,e,a){if(t&&t[e]&&t[e][a])return{prefix:e,iconName:a,icon:t[e][a]}}h&&((gt=(R.documentElement.doScroll?/^loaded|^c/:/^loaded|^i|^c/).test(R.readyState))||R.addEventListener("DOMContentLoaded",function t(){R.removeEventListener("DOMContentLoaded",t),gt=1,pt.map(function(t){return t()})}));var kt=function(t,e,a,n){var i,r,o,c,s,f=Object.keys(t),l=f.length,u=void 0!==n?(c=e,s=n,function(t,e,a,n){return c.call(s,t,e,a,n)}):e;for(o=void 0===a?(i=1,t[f[0]]):(i=0,a);i<l;i++)o=u(o,t[r=f[i]],r,t);return o};function wt(t){var e=function(t){for(var e=[],a=0,n=t.length;a<n;){var i=t.charCodeAt(a++);if(55296<=i&&i<=56319&&a<n){var r=t.charCodeAt(a++);56320==(64512&r)?e.push(((1023&i)<<10)+(1023&r)+65536):(e.push(i),a--)}else e.push(i)}return e}(t);return 1===e.length?e[0].toString(16):null}function xt(n){return Object.keys(n).reduce(function(t,e){var a=n[e];return!!a.icon?t[a.iconName]=a.icon:t[e]=a,t},{})}function At(t,e){var a=(2<arguments.length&&void 0!==arguments[2]?arguments[2]:{}).skipHooks,n=void 0!==a&&a,i=xt(e);"function"!=typeof vt.hooks.addPack||n?vt.styles[t]=I(I({},vt.styles[t]||{}),i):vt.hooks.addPack(t,xt(e)),"fas"===t&&At("fa",e)}var Ot=vt.styles,Nt=vt.shims,Ct=Object.values(z),Pt=null,St={},Et={},zt={},Mt={},jt={},It=Object.keys(E);function Lt(t,e){var a,n=e.split("-"),i=n[0],r=n.slice(1).join("-");return i!==t||""===r||(a=r,~G.indexOf(a))?null:r}var Rt,Tt=function(){var t=function(n){return kt(Ot,function(t,e,a){return t[a]=kt(e,n,{}),t},{})};St=t(function(e,t,a){(t[3]&&(e[t[3]]=a),t[2])&&t[2].filter(function(t){return"number"==typeof t}).forEach(function(t){e[t.toString(16)]=a});return e}),Et=t(function(e,t,a){(e[a]=a,t[2])&&t[2].filter(function(t){return"string"==typeof t}).forEach(function(t){e[t]=a});return e}),jt=t(function(e,t,a){var n=t[2];return e[a]=a,n.forEach(function(t){e[t]=a}),e});var r="far"in Ot||Q.autoFetchSvg,e=kt(Nt,function(t,e){var a=e[0],n=e[1],i=e[2];return"far"!==n||r||(n="fas"),"string"==typeof a&&(t.names[a]={prefix:n,iconName:i}),"number"==typeof a&&(t.unicodes[a.toString(16)]={prefix:n,iconName:i}),t},{names:{},unicodes:{}});zt=e.names,Mt=e.unicodes,Pt=Ut(Q.styleDefault)};function Dt(t,e){return(St[t]||{})[e]}function Ft(t,e){return(jt[t]||{})[e]}function Yt(t){return zt[t]||{prefix:null,iconName:null}}function Ht(){return Pt}Rt=function(t){Pt=Ut(t.styleDefault)},Z.push(Rt),Tt();var Wt=function(){return{prefix:null,iconName:null,rest:[]}};function Ut(t){var e=E[t],a=Y[t]||Y[e],n=t in vt.styles?t:null;return a||n||null}function _t(t){var e=(1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}).skipLookups,r=void 0!==e&&e,o=null,a=t.reduce(function(t,e){var a=Lt(Q.familyPrefix,e);if(Ot[e]?(e=Ct.includes(e)?M[e]:e,o=e,t.prefix=e):-1<It.indexOf(e)?(o=e,t.prefix=Ut(e)):a?t.iconName=a:e!==Q.replacementClass&&t.rest.push(e),!r&&t.prefix&&t.iconName){var n="fa"===o?Yt(t.iconName):{},i=Ft(t.prefix,t.iconName);n.prefix&&(o=null),t.iconName=n.iconName||i||t.iconName,t.prefix=n.prefix||t.prefix,"far"!==t.prefix||Ot.far||!Ot.fas||Q.autoFetchSvg||(t.prefix="fas")}return t},Wt());return"fa"!==a.prefix&&"fa"!==o||(a.prefix=Ht()||"fas"),a}var Bt=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.definitions={}}var e,a,n;return e=t,(a=[{key:"add",value:function(){for(var a=this,t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];var i=e.reduce(this._pullDefinitions,{});Object.keys(i).forEach(function(t){a.definitions[t]=I(I({},a.definitions[t]||{}),i[t]),At(t,i[t]);var e=z[t];e&&At(e,i[t]),Tt()})}},{key:"reset",value:function(){this.definitions={}}},{key:"_pullDefinitions",value:function(o,t){var c=t.prefix&&t.iconName&&t.icon?{0:t}:t;return Object.keys(c).map(function(t){var e=c[t],a=e.prefix,n=e.iconName,i=e.icon,r=i[2];o[a]||(o[a]={}),0<r.length&&r.forEach(function(t){"string"==typeof t&&(o[a][t]=i)}),o[a][n]=i}),o}}])&&i(e.prototype,a),n&&i(e,n),t}(),Xt=[],qt={},Vt={},Gt=Object.keys(Vt);function Kt(t,e){for(var a=arguments.length,n=new Array(2<a?a-2:0),i=2;i<a;i++)n[i-2]=arguments[i];return(qt[t]||[]).forEach(function(t){e=t.apply(null,[e].concat(n))}),e}function Jt(t){for(var e=arguments.length,a=new Array(1<e?e-1:0),n=1;n<e;n++)a[n-1]=arguments[n];(qt[t]||[]).forEach(function(t){t.apply(null,a)})}function Qt(){var t=arguments[0],e=Array.prototype.slice.call(arguments,1);return Vt[t]?Vt[t].apply(null,e):void 0}function Zt(t){"fa"===t.prefix&&(t.prefix="fas");var e=t.iconName,a=t.prefix||Ht();if(e)return e=Ft(a,e)||e,yt($t.definitions,a,e)||yt(vt.styles,a,e)}var $t=new Bt,te={i2svg:function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};return h?(Jt("beforeI2svg",t),Qt("pseudoElements2svg",t),Qt("i2svg",t)):Promise.reject("Operation requires a DOM of some kind.")},watch:function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},e=t.autoReplaceSvgRoot;!1===Q.autoReplaceSvg&&(Q.autoReplaceSvg=!0),Q.observeMutations=!0,bt(function(){ae({autoReplaceSvgRoot:e}),Jt("watch",t)})}},ee={noAuto:function(){Q.autoReplaceSvg=!1,Q.observeMutations=!1,Jt("noAuto")},config:Q,dom:te,parse:{icon:function(t){if(null===t)return null;if("object"===r(t)&&t.prefix&&t.iconName)return{prefix:t.prefix,iconName:Ft(t.prefix,t.iconName)||t.iconName};if(Array.isArray(t)&&2===t.length){var e=0===t[1].indexOf("fa-")?t[1].slice(3):t[1],a=Ut(t[0]);return{prefix:a,iconName:Ft(a,e)||e}}if("string"==typeof t&&(-1<t.indexOf("".concat(Q.familyPrefix,"-"))||t.match(H))){var n=_t(t.split(" "),{skipLookups:!0});return{prefix:n.prefix||Ht(),iconName:Ft(n.prefix,n.iconName)||n.iconName}}if("string"==typeof t){var i=Ht();return{prefix:i,iconName:Ft(i,t)||t}}}},library:$t,findIconDefinition:Zt,toHtml:ht},ae=function(){var t=(0<arguments.length&&void 0!==arguments[0]?arguments[0]:{}).autoReplaceSvgRoot,e=void 0===t?R:t;(0<Object.keys(vt.styles).length||Q.autoFetchSvg)&&h&&Q.autoReplaceSvg&&ee.dom.i2svg({node:e})};function ne(e,t){return Object.defineProperty(e,"abstract",{get:t}),Object.defineProperty(e,"html",{get:function(){return e.abstract.map(function(t){return ht(t)})}}),Object.defineProperty(e,"node",{get:function(){if(h){var t=R.createElement("div");return t.innerHTML=e.html,t.children}}}),e}function ie(t){var e=t.icons,a=e.main,n=e.mask,i=t.prefix,r=t.iconName,o=t.transform,c=t.symbol,s=t.title,f=t.maskId,l=t.titleId,u=t.extra,m=t.watchable,d=void 0!==m&&m,v=n.found?n:a,p=v.width,g=v.height,b="fak"===i,h=[Q.replacementClass,r?"".concat(Q.familyPrefix,"-").concat(r):""].filter(function(t){return-1===u.classes.indexOf(t)}).filter(function(t){return""!==t||!!t}).concat(u.classes).join(" "),y={children:[],attributes:I(I({},u.attributes),{},{"data-prefix":i,"data-icon":r,class:h,role:u.attributes.role||"img",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 ".concat(p," ").concat(g)})},k=b&&!~u.classes.indexOf("fa-fw")?{width:"".concat(p/g*16*.0625,"em")}:{};d&&(y.attributes[j]=""),s&&(y.children.push({tag:"title",attributes:{id:y.attributes["aria-labelledby"]||"title-".concat(l||at())},children:[s]}),delete y.attributes.title);var w,x,A,O,N,C,P,S=I(I({},y),{},{prefix:i,iconName:r,main:a,mask:n,maskId:f,transform:o,symbol:c,styles:I(I({},k),u.styles)}),E=n.found&&a.found?Qt("generateAbstractMask",S)||{children:[],attributes:{}}:Qt("generateAbstractIcon",S)||{children:[],attributes:{}},z=E.children,M=E.attributes;return S.children=z,S.attributes=M,c?(x=(w=S).prefix,A=w.iconName,O=w.children,N=w.attributes,C=w.symbol,P=!0===C?"".concat(x,"-").concat(Q.familyPrefix,"-").concat(A):C,[{tag:"svg",attributes:{style:"display: none;"},children:[{tag:"symbol",attributes:I(I({},N),{},{id:P}),children:O}]}]):function(t){var e=t.children,a=t.main,n=t.mask,i=t.attributes,r=t.styles,o=t.transform;if(ct(o)&&a.found&&!n.found){var c=a.width/a.height/2,s=.5;i.style=ot(I(I({},r),{},{"transform-origin":"".concat(c+o.x/16,"em ").concat(s+o.y/16,"em")}))}return[{tag:"svg",attributes:i,children:e}]}(S)}function re(t){var e=t.content,a=t.width,n=t.height,i=t.transform,r=t.title,o=t.extra,c=t.watchable,s=void 0!==c&&c,f=I(I(I({},o.attributes),r?{title:r}:{}),{},{class:o.classes.join(" ")});s&&(f[j]="");var l,u,m,d,v,p,g,b,h,y=I({},o.styles);ct(i)&&(y.transform=(u=(l={transform:i,startCentered:!0,width:a,height:n}).transform,m=l.width,d=void 0===m?A:m,v=l.height,p=void 0===v?A:v,g=l.startCentered,h="",h+=(b=void 0!==g&&g)&&x?"translate(".concat(u.x/$-d/2,"em, ").concat(u.y/$-p/2,"em) "):b?"translate(calc(-50% + ".concat(u.x/$,"em), calc(-50% + ").concat(u.y/$,"em)) "):"translate(".concat(u.x/$,"em, ").concat(u.y/$,"em) "),h+="scale(".concat(u.size/$*(u.flipX?-1:1),", ").concat(u.size/$*(u.flipY?-1:1),") "),h+="rotate(".concat(u.rotate,"deg) ")),y["-webkit-transform"]=y.transform);var k=ot(y);0<k.length&&(f.style=k);var w=[];return w.push({tag:"span",attributes:f,children:[e]}),r&&w.push({tag:"span",attributes:{class:"sr-only"},children:[r]}),w}var oe=vt.styles;function ce(t){var e=t[0],a=t[1],n=v(t.slice(4),1)[0];return{found:!0,width:e,height:a,icon:Array.isArray(n)?{tag:"g",attributes:{class:"".concat(Q.familyPrefix,"-").concat(V.GROUP)},children:[{tag:"path",attributes:{class:"".concat(Q.familyPrefix,"-").concat(V.SECONDARY),fill:"currentColor",d:n[0]}},{tag:"path",attributes:{class:"".concat(Q.familyPrefix,"-").concat(V.PRIMARY),fill:"currentColor",d:n[1]}}]}:{tag:"path",attributes:{fill:"currentColor",d:n}}}}var se={found:!1,width:512,height:512};function fe(r,o){var c=o;return"fa"===o&&null!==Q.styleDefault&&(o=Ht()),new Promise(function(t,e){var a,n;Qt("missingIconAbstract");if("fa"===c){var i=Yt(r)||{};r=i.iconName||r,o=i.prefix||o}if(r&&o&&oe[o]&&oe[o][r])return t(ce(oe[o][r]));a=r,n=o,S||Q.showMissingIcons||!a||console.error('Icon with name "'.concat(a,'" and prefix "').concat(n,'" is missing.')),t(I(I({},se),{},{icon:Q.showMissingIcons&&r&&Qt("missingIconAbstract")||{}}))})}var le=function(){},ue=Q.measurePerformance&&g&&g.mark&&g.measure?g:{mark:le,measure:le},me='FA "6.0.0-beta3"',de=function(t){ue.mark("".concat(me," ").concat(t," ends")),ue.measure("".concat(me," ").concat(t),"".concat(me," ").concat(t," begins"),"".concat(me," ").concat(t," ends"))},ve={begin:function(t){return ue.mark("".concat(me," ").concat(t," begins")),function(){return de(t)}},end:de},pe=function(){};function ge(t){return"string"==typeof(t.getAttribute?t.getAttribute(j):null)}function be(t){return R.createElementNS("http://www.w3.org/2000/svg",t)}function he(t){return R.createElement(t)}var ye={replace:function(t){var e,a=t[0];if(a.parentNode)if(t[1].forEach(function(t){a.parentNode.insertBefore(function e(a){var t=(1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}).ceFn,n=void 0===t?"svg"===a.tag?be:he:t;if("string"==typeof a)return R.createTextNode(a);var i=n(a.tag);return Object.keys(a.attributes||[]).forEach(function(t){i.setAttribute(t,a.attributes[t])}),(a.children||[]).forEach(function(t){i.appendChild(e(t,{ceFn:n}))}),i}(t),a)}),null===a.getAttribute(j)&&Q.keepOriginalSource){var n=R.createComment((e=" ".concat(a.outerHTML," "),e="".concat(e,"Font Awesome fontawesome.com ")));a.parentNode.replaceChild(n,a)}else a.remove()},nest:function(t){var e=t[0],a=t[1];if(~it(e).indexOf(Q.replacementClass))return ye.replace(t);var n=new RegExp("".concat(Q.familyPrefix,"-.*"));if(delete a[0].attributes.id,a[0].attributes.class){var i=a[0].attributes.class.split(" ").reduce(function(t,e){return e===Q.replacementClass||e.match(n)?t.toSvg.push(e):t.toNode.push(e),t},{toNode:[],toSvg:[]});a[0].attributes.class=i.toSvg.join(" "),0===i.toNode.length?e.removeAttribute("class"):e.setAttribute("class",i.toNode.join(" "))}var r=a.map(function(t){return ht(t)}).join("\n");e.setAttribute(j,""),e.innerHTML=r}};function ke(t){t()}function we(a,t){var n="function"==typeof t?t:pe;if(0===a.length)n();else{var e=ke;Q.mutateApproach===C&&(e=L.requestAnimationFrame||ke),e(function(){var t=!0===Q.autoReplaceSvg?ye.replace:ye[Q.autoReplaceSvg]||ye.replace,e=ve.begin("mutate");a.map(t),e(),n()})}}var xe=!1;function Ae(){xe=!0}function Oe(){xe=!1}var Ne=null;function Ce(t){if(p&&Q.observeMutations){var e=t.treeCallback,f=void 0===e?pe:e,a=t.nodeCallback,l=void 0===a?pe:a,n=t.pseudoElementsCallback,u=void 0===n?pe:n,i=t.observeMutationsRoot,r=void 0===i?R:i;Ne=new p(function(t){if(!xe){var s=Ht();nt(t).forEach(function(t){if("childList"===t.type&&0<t.addedNodes.length&&!ge(t.addedNodes[0])&&(Q.searchPseudoElements&&u(t.target),f(t.target)),"attributes"===t.type&&t.target.parentNode&&Q.searchPseudoElements&&u(t.target.parentNode),"attributes"===t.type&&ge(t.target)&&~q.indexOf(t.attributeName))if("class"===t.attributeName&&(r=t.target,o=r.getAttribute?r.getAttribute(D):null,c=r.getAttribute?r.getAttribute(F):null,o&&c)){var e=_t(it(t.target)),a=e.prefix,n=e.iconName;t.target.setAttribute(D,a||s),n&&t.target.setAttribute(F,n)}else(i=t.target)&&i.classList&&i.classList.contains&&i.classList.contains(Q.replacementClass)&&l(t.target);var i,r,o,c})}}),h&&Ne.observe(r,{childList:!0,attributes:!0,characterData:!0,subtree:!0})}}function Pe(t){var e,a,n=t.getAttribute("data-prefix"),i=t.getAttribute("data-icon"),r=void 0!==t.innerText?t.innerText.trim():"",o=_t(it(t));return o.prefix||(o.prefix=Ht()),n&&i&&(o.prefix=n,o.iconName=i),o.iconName&&o.prefix||o.prefix&&0<r.length&&(o.iconName=(e=o.prefix,a=t.innerText,(Et[e]||{})[a]||Dt(o.prefix,wt(t.innerText)))),o}function Se(t){var e,a,n,i,r,o,c=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{styleParser:!0},s=Pe(t),f=s.iconName,l=s.prefix,u=s.rest,m=(a=nt((e=t).attributes).reduce(function(t,e){return"class"!==t.name&&"style"!==t.name&&(t[e.name]=e.value),t},{}),n=e.getAttribute("title"),i=e.getAttribute("data-fa-title-id"),Q.autoA11y&&(n?a["aria-labelledby"]="".concat(Q.replacementClass,"-title-").concat(i||at()):(a["aria-hidden"]="true",a.focusable="false")),a),d=Kt("parseNodeAttributes",{},t),v=c.styleParser?(r=t.getAttribute("style"),o=[],r&&(o=r.split(";").reduce(function(t,e){var a=e.split(":"),n=a[0],i=a.slice(1);return n&&0<i.length&&(t[n]=i.join(":").trim()),t},{})),o):[];return I({iconName:f,title:t.getAttribute("title"),titleId:t.getAttribute("data-fa-title-id"),prefix:l,transform:tt,mask:{iconName:null,prefix:null,rest:[]},maskId:null,symbol:!1,extra:{classes:u,styles:v,attributes:m}},d)}var Ee=vt.styles;function ze(t){var e="nest"===Q.autoReplaceSvg?Se(t,{styleParser:!1}):Se(t);return~e.extra.classes.indexOf(W)?Qt("generateLayersText",t,e):Qt("generateSvgReplacementMutation",t,e)}function Me(t){var n=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;if(!h)return Promise.resolve();var e=R.documentElement.classList,i=function(t){return e.add("".concat(N,"-").concat(t))},r=function(t){return e.remove("".concat(N,"-").concat(t))},a=Q.autoFetchSvg?Object.keys(E):Object.keys(Ee),o=[".".concat(W,":not([").concat(j,"])")].concat(a.map(function(t){return".".concat(t,":not([").concat(j,"])")})).join(", ");if(0===o.length)return Promise.resolve();var c=[];try{c=nt(t.querySelectorAll(o))}catch(t){}if(!(0<c.length))return Promise.resolve();i("pending"),r("complete");var s=ve.begin("onTree"),f=c.reduce(function(t,e){try{var a=ze(e);a&&t.push(a)}catch(t){S||"MissingIcon"===t.name&&console.error(t)}return t},[]);return new Promise(function(e,a){Promise.all(f).then(function(t){we(t,function(){i("active"),i("complete"),r("pending"),"function"==typeof n&&n(),s(),e()})}).catch(function(t){s(),a(t)})})}function je(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;ze(t).then(function(t){t&&we([t],e)})}var Ie=function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},a=e.transform,n=void 0===a?tt:a,i=e.symbol,r=void 0!==i&&i,o=e.mask,c=void 0===o?null:o,s=e.maskId,f=void 0===s?null:s,l=e.title,u=void 0===l?null:l,m=e.titleId,d=void 0===m?null:m,v=e.classes,p=void 0===v?[]:v,g=e.attributes,b=void 0===g?{}:g,h=e.styles,y=void 0===h?{}:h;if(t){var k=t.prefix,w=t.iconName,x=t.icon;return ne(I({type:"icon"},t),function(){return Jt("beforeDOMElementCreation",{iconDefinition:t,params:e}),Q.autoA11y&&(u?b["aria-labelledby"]="".concat(Q.replacementClass,"-title-").concat(d||at()):(b["aria-hidden"]="true",b.focusable="false")),ie({icons:{main:ce(x),mask:c?ce(c.icon):{found:!1,width:null,height:null,icon:{}}},prefix:k,iconName:w,transform:I(I({},tt),n),symbol:r,title:u,maskId:f,titleId:d,extra:{attributes:b,styles:y,classes:p}})})}},Le={mixout:function(){return{icon:(i=Ie,function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},a=(t||{}).icon?t:Zt(t||{}),n=e.mask;return n&&(n=(n||{}).icon?n:Zt(n||{})),i(a,I(I({},e),{},{mask:n}))})};var i},hooks:function(){return{mutationObserverCallbacks:function(t){return t.treeCallback=Me,t.nodeCallback=je,t}}},provides:function(t){t.i2svg=function(t){var e=t.node,a=void 0===e?R:e,n=t.callback;return Me(a,void 0===n?function(){}:n)},t.generateSvgReplacementMutation=function(r,t){var o=t.iconName,c=t.title,s=t.titleId,f=t.prefix,l=t.transform,u=t.symbol,e=t.mask,m=t.maskId,d=t.extra;return new Promise(function(i,t){Promise.all([fe(o,f),e.iconName?fe(e.iconName,e.prefix):Promise.resolve({found:!1,width:512,height:512,icon:{}})]).then(function(t){var e=v(t,2),a=e[0],n=e[1];i([r,ie({icons:{main:a,mask:n},prefix:f,iconName:o,transform:l,symbol:u,maskId:m,title:c,titleId:s,extra:d,watchable:!0})])}).catch(t)})},t.generateAbstractIcon=function(t){var e,a=t.children,n=t.attributes,i=t.main,r=t.transform,o=ot(t.styles);return 0<o.length&&(n.style=o),ct(r)&&(e=Qt("generateAbstractTransformGrouping",{main:i,transform:r,containerWidth:i.width,iconWidth:i.width})),a.push(e||i.icon),{children:a,attributes:n}}}},Re={mixout:function(){return{layer:function(t){var a=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},e=a.classes,n=void 0===e?[]:e;return ne({type:"layer"},function(){Jt("beforeDOMElementCreation",{assembler:t,params:a});var e=[];return t(function(t){Array.isArray(t)?t.map(function(t){e=e.concat(t.abstract)}):e=e.concat(t.abstract)}),[{tag:"span",attributes:{class:["".concat(Q.familyPrefix,"-layers")].concat(m(n)).join(" ")},children:e}]})}}}},Te={mixout:function(){return{counter:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},a=e.title,n=void 0===a?null:a,i=e.classes,r=void 0===i?[]:i,o=e.attributes,c=void 0===o?{}:o,s=e.styles,f=void 0===s?{}:s;return ne({type:"counter",content:t},function(){return Jt("beforeDOMElementCreation",{content:t,params:e}),function(t){var e=t.content,a=t.title,n=t.extra,i=I(I(I({},n.attributes),a?{title:a}:{}),{},{class:n.classes.join(" ")}),r=ot(n.styles);0<r.length&&(i.style=r);var o=[];return o.push({tag:"span",attributes:i,children:[e]}),a&&o.push({tag:"span",attributes:{class:"sr-only"},children:[a]}),o}({content:t.toString(),title:n,extra:{attributes:c,styles:f,classes:["".concat(Q.familyPrefix,"-layers-counter")].concat(m(r))}})})}}}},De={mixout:function(){return{text:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},a=e.transform,n=void 0===a?tt:a,i=e.title,r=void 0===i?null:i,o=e.classes,c=void 0===o?[]:o,s=e.attributes,f=void 0===s?{}:s,l=e.styles,u=void 0===l?{}:l;return ne({type:"text",content:t},function(){return Jt("beforeDOMElementCreation",{content:t,params:e}),re({content:t,transform:I(I({},tt),n),title:r,extra:{attributes:f,styles:u,classes:["".concat(Q.familyPrefix,"-layers-text")].concat(m(c))}})})}}},provides:function(t){t.generateLayersText=function(t,e){var a=e.title,n=e.transform,i=e.extra,r=null,o=null;if(x){var c=parseInt(getComputedStyle(t).fontSize,10),s=t.getBoundingClientRect();r=s.width/c,o=s.height/c}return Q.autoA11y&&!a&&(i.attributes["aria-hidden"]="true"),Promise.resolve([t,re({content:t.innerHTML,width:r,height:o,transform:n,title:a,extra:i,watchable:!0})])}}},Fe=new RegExp('"',"ug"),Ye=[1105920,1112319];function He(z,M){var j="".concat(O).concat(M.replace(":","-"));return new Promise(function(n,t){if(null!==z.getAttribute(j))return n();var e,a,i,r,o,c,s,f,l,u,m,d,v=nt(z.children).filter(function(t){return t.getAttribute(T)===M})[0],p=L.getComputedStyle(z,M),g=p.getPropertyValue("font-family").match(U),b=p.getPropertyValue("font-weight"),h=p.getPropertyValue("content");if(v&&!g)return z.removeChild(v),n();if(g&&"none"!==h&&""!==h){var y=p.getPropertyValue("content"),k=~["Solid","Regular","Light","Thin","Duotone","Brands","Kit"].indexOf(g[2])?Y[g[2].toLowerCase()]:_[b],w=(l=y.replace(Fe,""),o=0,s=(r=l).length,u=55296<=(f=r.charCodeAt(o))&&f<=56319&&o+1<s&&56320<=(c=r.charCodeAt(o+1))&&c<=57343?1024*(f-55296)+c-56320+65536:f,m=u>=Ye[0]&&u<=Ye[1],{value:wt((d=2===l.length&&l[0]===l[1])?l[0]:l),isSecondary:m||d}),x=w.value,A=w.isSecondary,O=g[0].startsWith("FontAwesome"),N=Dt(k,x),C=N;if(O){var P=(a=Mt[e=x],i=Dt("fas",e),a||(i?{prefix:"fas",iconName:i}:null)||{prefix:null,iconName:null});P.iconName&&P.prefix&&(N=P.iconName,k=P.prefix)}if(!N||A||v&&v.getAttribute(D)===k&&v.getAttribute(F)===C)n();else{z.setAttribute(j,C),v&&z.removeChild(v);var S={iconName:null,title:null,titleId:null,prefix:null,transform:tt,symbol:!1,mask:{iconName:null,prefix:null,rest:[]},maskId:null,extra:{classes:[],styles:{},attributes:{}}},E=S.extra;E.attributes[T]=M,fe(N,k).then(function(t){var e=ie(I(I({},S),{},{icons:{main:t,mask:Wt()},prefix:k,iconName:C,extra:E,watchable:!0})),a=R.createElement("svg");"::before"===M?z.insertBefore(a,z.firstChild):z.appendChild(a),a.outerHTML=e.map(function(t){return ht(t)}).join("\n"),z.removeAttribute(j),n()}).catch(t)}}else n()})}function We(t){return Promise.all([He(t,"::before"),He(t,"::after")])}function Ue(t){return!(t.parentNode===document.head||~P.indexOf(t.tagName.toUpperCase())||t.getAttribute(T)||t.parentNode&&"svg"===t.parentNode.tagName)}function _e(i){if(h)return new Promise(function(t,e){var a=nt(i.querySelectorAll("*")).filter(Ue).map(We),n=ve.begin("searchPseudoElements");Ae(),Promise.all(a).then(function(){n(),Oe(),t()}).catch(function(){n(),Oe(),e()})})}var Be=!1,Xe=function(t){return t.toLowerCase().split(" ").reduce(function(t,e){var a=e.toLowerCase().split("-"),n=a[0],i=a.slice(1).join("-");if(n&&"h"===i)return t.flipX=!0,t;if(n&&"v"===i)return t.flipY=!0,t;if(i=parseFloat(i),isNaN(i))return t;switch(n){case"grow":t.size=t.size+i;break;case"shrink":t.size=t.size-i;break;case"left":t.x=t.x-i;break;case"right":t.x=t.x+i;break;case"up":t.y=t.y-i;break;case"down":t.y=t.y+i;break;case"rotate":t.rotate=t.rotate+i}return t},{size:16,x:0,y:0,flipX:!1,flipY:!1,rotate:0})},qe={x:0,y:0,width:"100%",height:"100%"};function Ve(t){var e=!(1<arguments.length&&void 0!==arguments[1])||arguments[1];return t.attributes&&(t.attributes.fill||e)&&(t.attributes.fill="black"),t}var Ge;Ge={mixoutsTo:ee}.mixoutsTo,Xt=[mt,Le,Re,Te,De,{hooks:function(){return{mutationObserverCallbacks:function(t){return t.pseudoElementsCallback=_e,t}}},provides:function(t){t.pseudoElements2svg=function(t){var e=t.node,a=void 0===e?R:e;Q.searchPseudoElements&&_e(a)}}},{mixout:function(){return{dom:{unwatch:function(){Ae(),Be=!0}}}},hooks:function(){return{bootstrap:function(){Ce(Kt("mutationObserverCallbacks",{}))},noAuto:function(){Ne&&Ne.disconnect()},watch:function(t){var e=t.observeMutationsRoot;Be?Oe():Ce(Kt("mutationObserverCallbacks",{observeMutationsRoot:e}))}}}},{mixout:function(){return{parse:{transform:function(t){return Xe(t)}}}},hooks:function(){return{parseNodeAttributes:function(t,e){var a=e.getAttribute("data-fa-transform");return a&&(t.transform=Xe(a)),t}}},provides:function(t){t.generateAbstractTransformGrouping=function(t){var e=t.main,a=t.transform,n=t.containerWidth,i=t.iconWidth,r={transform:"translate(".concat(n/2," 256)")},o="translate(".concat(32*a.x,", ").concat(32*a.y,") "),c="scale(".concat(a.size/16*(a.flipX?-1:1),", ").concat(a.size/16*(a.flipY?-1:1),") "),s="rotate(".concat(a.rotate," 0 0)"),f={outer:r,inner:{transform:"".concat(o," ").concat(c," ").concat(s)},path:{transform:"translate(".concat(i/2*-1," -256)")}};return{tag:"g",attributes:I({},f.outer),children:[{tag:"g",attributes:I({},f.inner),children:[{tag:e.icon.tag,children:e.icon.children,attributes:I(I({},e.icon.attributes),f.path)}]}]}}}},{hooks:function(){return{parseNodeAttributes:function(t,e){var a=e.getAttribute("data-fa-mask"),n=a?_t(a.split(" ").map(function(t){return t.trim()})):Wt();return n.prefix||(n.prefix=Ht()),t.mask=n,t.maskId=e.getAttribute("data-fa-mask-id"),t}}},provides:function(t){t.generateAbstractMask=function(t){var e,a,n,i,r,o,c,s,f,l=t.children,u=t.attributes,m=t.main,d=t.mask,v=t.maskId,p=t.transform,g=m.width,b=m.icon,h=d.width,y=d.icon,k=(a=(e={transform:p,containerWidth:h,iconWidth:g}).transform,n=e.containerWidth,i=e.iconWidth,r={transform:"translate(".concat(n/2," 256)")},o="translate(".concat(32*a.x,", ").concat(32*a.y,") "),c="scale(".concat(a.size/16*(a.flipX?-1:1),", ").concat(a.size/16*(a.flipY?-1:1),") "),s="rotate(".concat(a.rotate," 0 0)"),{outer:r,inner:{transform:"".concat(o," ").concat(c," ").concat(s)},path:{transform:"translate(".concat(i/2*-1," -256)")}}),w={tag:"rect",attributes:I(I({},qe),{},{fill:"white"})},x=b.children?{children:b.children.map(Ve)}:{},A={tag:"g",attributes:I({},k.inner),children:[Ve(I({tag:b.tag,attributes:I(I({},b.attributes),k.path)},x))]},O={tag:"g",attributes:I({},k.outer),children:[A]},N="mask-".concat(v||at()),C="clip-".concat(v||at()),P={tag:"mask",attributes:I(I({},qe),{},{id:N,maskUnits:"userSpaceOnUse",maskContentUnits:"userSpaceOnUse"}),children:[w,O]},S={tag:"defs",children:[{tag:"clipPath",attributes:{id:C},children:(f=y,"g"===f.tag?f.children:[f])},P]};return l.push(S,{tag:"rect",attributes:I({fill:"currentColor","clip-path":"url(#".concat(C,")"),mask:"url(#".concat(N,")")},qe)}),{children:l,attributes:u}}}},{provides:function(t){var r=!1;L.matchMedia&&(r=L.matchMedia("(prefers-reduced-motion: reduce)").matches),t.missingIconAbstract=function(){var t=[],e={fill:"currentColor"},a={attributeType:"XML",repeatCount:"indefinite",dur:"2s"};t.push({tag:"path",attributes:I(I({},e),{},{d:"M156.5,447.7l-12.6,29.5c-18.7-9.5-35.9-21.2-51.5-34.9l22.7-22.7C127.6,430.5,141.5,440,156.5,447.7z M40.6,272H8.5 c1.4,21.2,5.4,41.7,11.7,61.1L50,321.2C45.1,305.5,41.8,289,40.6,272z M40.6,240c1.4-18.8,5.2-37,11.1-54.1l-29.5-12.6 C14.7,194.3,10,216.7,8.5,240H40.6z M64.3,156.5c7.8-14.9,17.2-28.8,28.1-41.5L69.7,92.3c-13.7,15.6-25.5,32.8-34.9,51.5 L64.3,156.5z M397,419.6c-13.9,12-29.4,22.3-46.1,30.4l11.9,29.8c20.7-9.9,39.8-22.6,56.9-37.6L397,419.6z M115,92.4 c13.9-12,29.4-22.3,46.1-30.4l-11.9-29.8c-20.7,9.9-39.8,22.6-56.8,37.6L115,92.4z M447.7,355.5c-7.8,14.9-17.2,28.8-28.1,41.5 l22.7,22.7c13.7-15.6,25.5-32.9,34.9-51.5L447.7,355.5z M471.4,272c-1.4,18.8-5.2,37-11.1,54.1l29.5,12.6 c7.5-21.1,12.2-43.5,13.6-66.8H471.4z M321.2,462c-15.7,5-32.2,8.2-49.2,9.4v32.1c21.2-1.4,41.7-5.4,61.1-11.7L321.2,462z M240,471.4c-18.8-1.4-37-5.2-54.1-11.1l-12.6,29.5c21.1,7.5,43.5,12.2,66.8,13.6V471.4z M462,190.8c5,15.7,8.2,32.2,9.4,49.2h32.1 c-1.4-21.2-5.4-41.7-11.7-61.1L462,190.8z M92.4,397c-12-13.9-22.3-29.4-30.4-46.1l-29.8,11.9c9.9,20.7,22.6,39.8,37.6,56.9 L92.4,397z M272,40.6c18.8,1.4,36.9,5.2,54.1,11.1l12.6-29.5C317.7,14.7,295.3,10,272,8.5V40.6z M190.8,50 c15.7-5,32.2-8.2,49.2-9.4V8.5c-21.2,1.4-41.7,5.4-61.1,11.7L190.8,50z M442.3,92.3L419.6,115c12,13.9,22.3,29.4,30.5,46.1 l29.8-11.9C470,128.5,457.3,109.4,442.3,92.3z M397,92.4l22.7-22.7c-15.6-13.7-32.8-25.5-51.5-34.9l-12.6,29.5 C370.4,72.1,384.4,81.5,397,92.4z"})});var n=I(I({},a),{},{attributeName:"opacity"}),i={tag:"circle",attributes:I(I({},e),{},{cx:"256",cy:"364",r:"28"}),children:[]};return r||i.children.push({tag:"animate",attributes:I(I({},a),{},{attributeName:"r",values:"28;14;28;28;14;28;"})},{tag:"animate",attributes:I(I({},n),{},{values:"1;0;1;1;0;1;"})}),t.push(i),t.push({tag:"path",attributes:I(I({},e),{},{opacity:"1",d:"M263.7,312h-16c-6.6,0-12-5.4-12-12c0-71,77.4-63.9,77.4-107.8c0-20-17.8-40.2-57.4-40.2c-29.1,0-44.3,9.6-59.2,28.7 c-3.9,5-11.1,6-16.2,2.4l-13.1-9.2c-5.6-3.9-6.9-11.8-2.6-17.2c21.2-27.2,46.4-44.7,91.2-44.7c52.3,0,97.4,29.8,97.4,80.2 c0,67.6-77.4,63.5-77.4,107.8C275.7,306.6,270.3,312,263.7,312z"}),children:r?[]:[{tag:"animate",attributes:I(I({},n),{},{values:"1;0;0;0;0;1;"})}]}),r||t.push({tag:"path",attributes:I(I({},e),{},{opacity:"0",d:"M232.5,134.5l7,168c0.3,6.4,5.6,11.5,12,11.5h9c6.4,0,11.7-5.1,12-11.5l7-168c0.3-6.8-5.2-12.5-12-12.5h-23 C237.7,122,232.2,127.7,232.5,134.5z"}),children:[{tag:"animate",attributes:I(I({},n),{},{values:"0;0;1;1;0;0;"})}]}),{tag:"g",attributes:{class:"missing"},children:t}}}},{hooks:function(){return{parseNodeAttributes:function(t,e){var a=e.getAttribute("data-fa-symbol"),n=null!==a&&(""===a||a);return t.symbol=n,t}}}}],qt={},Object.keys(Vt).forEach(function(t){-1===Gt.indexOf(t)&&delete Vt[t]}),Xt.forEach(function(t){var a=t.mixout?t.mixout():{};if(Object.keys(a).forEach(function(e){"function"==typeof a[e]&&(Ge[e]=a[e]),"object"===r(a[e])&&Object.keys(a[e]).forEach(function(t){Ge[e]||(Ge[e]={}),Ge[e][t]=a[e][t]})}),t.hooks){var e=t.hooks();Object.keys(e).forEach(function(t){qt[t]||(qt[t]=[]),qt[t].push(e[t])})}t.provides&&t.provides(Vt)}),function(t){try{for(var e=arguments.length,a=new Array(1<e?e-1:0),n=1;n<e;n++)a[n-1]=arguments[n];t.apply(void 0,a)}catch(t){if(!S)throw t}}(function(t){b&&(L.FontAwesome||(L.FontAwesome=ee),bt(function(){ae(),Jt("bootstrap")})),vt.hooks=I(I({},vt.hooks),{},{addPack:function(t,e){vt.styles[t]=I(I({},vt.styles[t]||{}),e),Tt(),ae()},addPacks:function(t){t.forEach(function(t){var e=v(t,2),a=e[0],n=e[1];vt.styles[a]=I(I({},vt.styles[a]||{}),n)}),Tt(),ae()},addShims:function(t){var e;(e=vt.shims).push.apply(e,m(t)),Tt(),ae()}})})}();