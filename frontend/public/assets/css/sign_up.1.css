main {
    margin: 0;
    padding: 0;
    outline: none;
}

.mapage {
    background: linear-gradient(45deg, #F2F8F1, #262D2A);
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.container {
    position: relative;
   justify-content: right;
}

form {
    /* background: rgba(255, 255, 255, 0.3); */
    padding: 3rem;
    height: 100%;
    width: 40%;
    border-radius: 20px;
    border-left: 1px solid rgba(255, 255, 255, .3);
    border-top: 1px solid rgba(255, 255, 255, .3);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    -moz-backdrop-filter: blur(10px);
    /* box-shadow: 20px 20px 40px -6px rgba(0, 0, 0, .2); */
    /* text-align: center; */
}



/* 
 .form-register form {
    background: rgba(255, 255, 255, 0.3);
    padding: 0;
} */


.adopte-logo-login {
    width: 40%;
}

p {
    color: white;
    font-weight: 500;
    opacity: .7;
    font-size: 2rem;
    margin-bottom: 40px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, .2);
}

form a {
    color: #ddd;
    text-decoration: none;
    font-size: 12px;
}

h3 a {
color: #262D2A;
font-size: 1.5rem;
}


form a:hover {
    text-shadow: 2px 2px 6px rgb(0, 0, 0);
}

input {
    background: transparent;
    border: none;
    border-left: 1px solid rgba(255, 255, 255, .3);
    border-top: 1px solid rgba(255, 255, 255, .3);
    padding: 1rem;
    width: 20rem;
    border-radius: 50px;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    -moz-backdrop-filter: blur(5px);
    box-shadow: 4px 4px 10px rgba(0, 0, 0, .2);
    color: white;
    font-weight: 500;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, .2);
    transition: all .3s;
    margin-bottom: 2em;
}

::placeholder {
    color: white;
}

input:hover,
input[type="email"]:focus,
input[type="password"]:focus {
    background: rgba(0, 0, 0, .1);
    box-shadow: 4px 4px 60px 8px rgba(0, 0, 0, .2);
}

input[type="submit"] {
    cursor: pointer;
    margin-top: 10px;
    font-size: 1rem;
    width: 150px;
}


/* Shadows */

.drop {
    background: rgba(255, 255, 255, .3);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    -moz-backdrop-filter: blur(10px);
    position: absolute;
    border-left: 1px solid rgba(255, 255, 255, .3);
    border-top: 1px solid rgba(255, 255, 255, .3);
    border-radius: 10px;
    box-shadow: 10px 10px 60px -8px rgba(0, 0, 0, .2);
}

.drop-1 {
    height: 80px;
    width: 80px;
    top: -30px;
    left: 740px;
    /* z-index: -1; */
}

.drop-2 {
    height: 80px;
    width: 80px;
    bottom: -30px;
    right: -10px;
}

.drop-3 {
    height: 100px;
    width: 100px;
    bottom: 120px;
    right: -50px;
    /* z-index: -1; */
}

.drop-4 {
    height: 120px;
    width: 120px;
    top: -60px;
    right: -50px;
}

.drop-5 {
    height: 60px;
    width: 60px;
    bottom: 250px;
    left: 800px;
    /* z-index: -1; */
}

@media screen and (max-width: 991px) {
    .container {
        position: relative;
        display: flex;
        justify-content: center;
    }
}